<?php

declare(strict_types=1);

namespace App\Controller;

use App\Entity\Main\UnloadingPoint;
use App\Form\Type\ExportType;
use App\Form\Type\OrderReportType;
use App\Repository\Main\OrderRepository;
use App\Repository\Main\UnloadingPointRepository;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Knp\Component\Pager\PaginatorInterface;
use Symfony\Bridge\Twig\Attribute\Template;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class ManagementController.
 */
class ManagementController extends AbstractController
{
    /**
     * DefaultController constructor.
     */
    public function __construct(private readonly EntityManagerInterface $manager)
    {
    }

    #[Route(path: '/management', name: 'management')]
    public function management(): RedirectResponse
    {
        return $this->redirectToRoute(route: 'management_contractArea');
    }

    /**
     * Auftragsuebersicht.
     */
    #[Template('management/order_report.html.twig')]
    #[Route(path: '/management/order/overview/{dateFrom}/{dateTo}', name: 'management_order_report')]
    /**
     * @return RedirectResponse|array<string, mixed>
     */
    public function orderReport(Request $request, OrderRepository $orderRepo, PaginatorInterface $paginator,
        ?\DateTime $dateFrom = null, ?\DateTime $dateTo = null): RedirectResponse|array
    {
        if (!$this->isGranted(attribute: 'ROLE_REPORT')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }

        $orderBy = $request->query->get(key: 'orderBy');
        $order = $request->query->get(key: 'order');

        // switch for ASC and DESC may also be done by paginator
        $sortOrder = ['date' => null, 'contractArea' => null, 'providerName' => null, 'id' => null, 'cp_dsdid' => null,
            'ul_dsdid' => null, 'status' => null];

        $key = lcfirst(string: $orderBy ?? '');
        if (array_key_exists(key: $key, array: $sortOrder)) {
            $sortOrder[$key] = match ($order) {
                'ASC' => 'DESC',
                default => 'ASC',
            };
        } // end of switch for ASC and DESC

        /** @var UnloadingPointRepository $unloadingPointRepo */
        $unloadingPointRepo = $this->manager->getRepository(UnloadingPoint::class);

        $unloadingPointId = $request->query->get(key: 'unloadingPoint');

        $unloadingPoint = null;

        if ($unloadingPointId) {
            $unloadingPoint = $unloadingPointRepo->find(id: $unloadingPointId);
        }

        if (is_null(value: $dateFrom)) {
            $dateFrom = new \DateTime(datetime: 'first day of this month');
        }

        if (is_null(value: $dateTo)) {
            $dateTo = new \DateTime(datetime: 'last day of this month');
        }

        $unloadingPoints = $unloadingPointRepo->findAll();

        $form = $this->createForm(
            type: OrderReportType::class,
            data: [],
            options: ['unloadingPoint' => $unloadingPoints, 'dateFrom' => $dateFrom, 'dateTo' => $dateTo]
        );

        // init selected unloading point
        if ($unloadingPoint) {
            $form->get('unloadingPoint')->setData($unloadingPoint);
        }

        $orderByAndOrder = null;
        if ($orderBy && $order) {
            $orderByAndOrder = $orderBy.':'.$order;
        }
        $exportForm = $this->createForm(
            type: ExportType::class,
            data: [],
            options: ['unloadingPoint' => $unloadingPoint ? $unloadingPoint->getId() : null,
                'dateFrom' => $dateFrom,
                'dateTo' => $dateTo,
                'orderByAndOrder' => $orderByAndOrder,
                'action' => $this->generateUrl(route: 'management_report_download'),
                'method' => 'POST']
        );

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $requestParameter = [];

            $data = $form->getViewData();

            /* @var DateTime $data['dateFrom'] */
            $requestParameter['dateFrom'] = $data['dateFrom']->format('Y-m-d');

            /* @var DateTime $data['dateTo'] */
            $requestParameter['dateTo'] = $data['dateTo']->format('Y-m-d');

            if ($data['unloadingPoint']) {
                $requestParameter['unloadingPoint'] = $data['unloadingPoint']->getId();
            }

            return $this->redirectToRoute(
                route: 'management_order_report', parameters: $requestParameter);
        }

        $queryBuilder = $orderRepo->reportUnloadingPointQuery(
            startDate: $dateFrom, endDate: $dateTo, unloadingPoint: $unloadingPoint
        );

        $pagination = $paginator->paginate(
            $queryBuilder, // query NOT result
            $request->query->getInt(key: 'page', default: 1), // page number
            10 // limit per page
        );

        return [
            'pagination' => $pagination,
            'form' => $form->createView(),
            'exportForm' => $exportForm->createView(),
            'sortOrder' => $sortOrder, // switch for ASC and DESC, maybe use paginator function instead
        ];
    }
}
