<?php

declare(strict_types=1);

namespace App\Controller;

use App\Services\AccessHelper;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\SerializerInterface;

abstract class BaseCrudController extends AbstractController
{
    public const FORM_MODE_ADD = 'add';
    public const FORM_MODE_EDIT = 'edit';
    public const FORM_MODE_DELETE = 'delete';
    public const FORM_MODE_LOCK = 'lock';
    public const FORM_MODE_RESET = 'reset';

    /**
     * @param ServiceEntityRepository<object> $repository
     */
    public function __construct(protected ServiceEntityRepository $repository, protected SerializerInterface $serializer, protected EntityManagerInterface $entityManager, protected AccessHelper $accessHelper, protected string $controllerName, protected string $listTwig, protected string $detailTwig)
    {
    }

    protected function mapEntityToDto(object $entity, Request $request): object
    {
        return $entity;
    }

    protected function mapDtoToEntity(object $dto, object $entity, Request $request): object
    {
        return $dto;
    }

    /**
     * @param array<string, mixed> $options
     *
     * @return array<string, mixed>
     */
    protected function getFormOptions(object $dto, string $mode, array $options): array
    {
        return match ($mode) {
            BaseCrudController::FORM_MODE_ADD => array_merge($options, ['mode' => BaseCrudController::FORM_MODE_ADD, 'method' => Request::METHOD_POST]),
            BaseCrudController::FORM_MODE_EDIT => array_merge($options, ['mode' => BaseCrudController::FORM_MODE_EDIT, 'method' => Request::METHOD_PUT]),
            BaseCrudController::FORM_MODE_DELETE => array_merge($options, ['mode' => BaseCrudController::FORM_MODE_DELETE, 'method' => Request::METHOD_DELETE]),
            BaseCrudController::FORM_MODE_LOCK => array_merge($options, ['mode' => BaseCrudController::FORM_MODE_LOCK, 'method' => Request::METHOD_POST]),
            BaseCrudController::FORM_MODE_RESET => array_merge($options, ['mode' => BaseCrudController::FORM_MODE_RESET, 'method' => Request::METHOD_POST]),
            default => $options,
        };
    }

    /**
     * @param array<int, mixed> $list
     *
     * @return array<int, mixed>
     */
    protected function sortListView(array $list, string $column, int $order): array
    {
        array_multisort(array_column(array: $list, column_key: $column), $order, $list);

        return $list;
    }

    /**
     * @param array<int, object>   $entityList
     * @param array<string, mixed> $options
     *
     * @return array<string, mixed>
     */
    protected function getListViewOptions(array $entityList, array $options): array
    {
        return $options;
    }

    /**
     * @param array<string, mixed> $options
     *
     * @return array<string, mixed>
     */
    protected function getDetailViewOptions(object $entity, array $options, Request $request): array
    {
        return $options;
    }

    /**
     * @return array<string, mixed>
     */
    protected function getListFindBy(Request $request): array
    {
        return ['deleted' => false];
    }

    /**
     * @return array<string, mixed>
     */
    protected function getListSorting(Request $request): array
    {
        return [];
    }

    /**
     * @param array<string, mixed> $routeParams
     */
    protected function processList(Request $request, object $newEntity, string $formType, string $redirectRoute, array $routeParams): Response
    {
        $self = $this;

        $newDto = $this->mapEntityToDto(entity: $newEntity, request: $request);

        $editForm = $this->createForm(type: $formType, data: $newDto, options: $this->getFormOptions(dto: $newDto, mode: BaseCrudController::FORM_MODE_ADD, options: []));

        $editForm->handleRequest($request);

        if ($editForm->isSubmitted() && $editForm->isValid()) {
            $newEntity = $this->mapDtoToEntity(dto: $newDto, entity: $newEntity, request: $request);

            $this->entityManager->persist($newEntity);
            $this->entityManager->flush();

            $routeParams['noty'] = 'success';
            $routeParams['message'] = $routeParams['object'].' wurde erfolgreich angelegt.';

            return $this->redirectToRoute(route: $redirectRoute, parameters: $routeParams);
        }

        $items = $this->repository->findBy(criteria: $this->getListFindBy(request: $request), orderBy: $this->getListSorting(request: $request));

        $mappedList = array_map(callback: fn (object $entity): object => $self->mapEntityToDto(entity: $entity, request: $request), array: $items);

        return $this->render(view: $this->listTwig, parameters: $this->getListViewOptions(entityList: $items, options: [
            'controller_name' => $this->controllerName,
            'list' => $this->serializer->serialize($mappedList, 'json', ['groups' => ['list']]),
            'editForm' => $editForm->createView(),
        ]));
    }

    /**
     * @param array<string, mixed> $routeParams
     */
    protected function processDetails(string $uuid, Request $request, string $editFormType, string $deleteFormType, string $lockFormType, string $resetFormType, string $redirectRoute, array $routeParams): Response
    {
        $entity = $this->repository->findOneBy(criteria: ['uuid' => $uuid]);

        if (null === $entity) {
            throw $this->createNotFoundException();
        }

        $dto = $this->mapEntityToDto(entity: $entity, request: $request);

        $editForm = $this->createForm(type: $editFormType, data: $dto, options: $this->getFormOptions(dto: $dto, mode: BaseCrudController::FORM_MODE_EDIT, options: []));

        $editForm->handleRequest($request);

        if ($editForm->isSubmitted() && $editForm->isValid()) {
            $entity = $this->mapDtoToEntity(dto: $dto, entity: $entity, request: $request);

            $this->entityManager->persist($entity);
            $this->entityManager->flush();

            $routeParams['noty'] = 'success';
            $routeParams['message'] = $routeParams['object'].' wurde erfolgreich bearbeitet.';

            return $this->redirectToRoute(route: $redirectRoute, parameters: $routeParams);
        }

        $deleteForm = $this->createForm(type: $deleteFormType, options: $this->getFormOptions(dto: $dto, mode: BaseCrudController::FORM_MODE_DELETE, options: []));
        $lockForm = $this->createForm(type: $lockFormType, options: $this->getFormOptions(dto: $dto, mode: BaseCrudController::FORM_MODE_LOCK, options: []));
        $resetForm = $this->createForm(type: $resetFormType, options: $this->getFormOptions(dto: $dto, mode: BaseCrudController::FORM_MODE_RESET, options: []));

        return $this->render(view: $this->detailTwig, parameters: $this->getDetailViewOptions(entity: $entity, options: [
            'controller_name' => $this->controllerName,
            'item' => $dto,
            'editForm' => $editForm->createView(),
            'deleteForm' => $deleteForm->createView(),
            'lockForm' => $lockForm->createView(),
            'resetForm' => $resetForm->createView(),
        ], request: $request));
    }

    /**
     * @param array<string, mixed> $routeParams
     */
    protected function processDelete(string $uuid, Request $request, string $redirectRoute, array $routeParams): Response
    {
        $entity = $this->repository->findOneBy(criteria: ['uuid' => $uuid]);

        if (null === $entity) {
            throw $this->createNotFoundException();
        }

        $routeParams['noty'] = 'success';
        $routeParams['message'] = $routeParams['object'].' wurde erfolgreich gelöscht.';

        $entity->setDeleted(true);
        $this->entityManager->persist($entity);
        $this->entityManager->flush();

        return $this->redirectToRoute(route: $redirectRoute, parameters: $routeParams);
    }

    /**
     * @param array<string, mixed> $routeParams
     */
    protected function processLock(string $uuid, Request $request, string $redirectRoute, array $routeParams): Response
    {
        $entity = $this->repository->findOneBy(criteria: ['uuid' => $uuid]);

        if (null === $entity) {
            throw $this->createNotFoundException();
        }

        $entity->setLocked(!$entity->getLocked());
        $this->entityManager->persist($entity);
        $this->entityManager->flush();

        $routeParams['noty'] = 'success';
        $routeParams['message'] = $routeParams['object'].' wurde erfolgreich gesperrt.';

        if (!$entity->getLocked()) {
            $routeParams['message'] = $routeParams['object'].' wurde erfolgreich entsperrt.';
        }

        return $this->redirectToRoute(route: $redirectRoute, parameters: $routeParams);
    }

    /**
     * @param array<string, mixed> $routeParams
     */
    protected function processReset(string $uuid, Request $request, string $redirectRoute, array $routeParams): Response
    {
        return $this->redirectToRoute(route: $redirectRoute, parameters: $routeParams);
    }

    /*protected function createNamedForm(string $name, string $type, $data = null, array $options = [])
    {
        return $this->get('form.factory')->createNamed($name, $type, $data, $options);
    }*/
}
