<?php

declare(strict_types=1);

namespace App\Controller;

use App\Entity\Main\User;
use App\Form\Type\EmailEditType;
use App\Form\Type\LocaleEditType;
use App\Form\Type\PasswordSendType;
use App\Services\Mailer;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bridge\Twig\Attribute\Template;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Form\FormError;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class UserProfileController.
 */
class UserProfileController extends AbstractController
{
    /**
     * DefaultController constructor.
     */
    public function __construct(private readonly EntityManagerInterface $manager)
    {
    }

    /**
     * Benutzer Profil.
     */
    #[Template('user_profile/user_profile.html.twig')]
    #[Route(path: '/user-profile')]
    public function userProfile(): RedirectResponse
    {
        return $this->redirectToRoute(route: 'app_userprofile_userprofilenewpassword');
        // return [];
    }

    /**
     * Benutzer Profil.
     */
    #[Template('user_profile/user_profile_new_password.html.twig')]
    #[Route(path: '/user-profile/new-password')]
    /**
     * @return array<string, mixed>|Response
     */
    public function userProfileNewPassword(Request $request, Mailer $mailer): array|Response
    {
        $form = $this->createForm(
            type: PasswordSendType::class,
            data: []
        );

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $user = $this->getUser();
            if (!$user instanceof User) {
                throw $this->createAccessDeniedException();
            }
            try {
                $mailer->sendPasswordReset(user: $user);
            } catch (\Exception $e) {
                throw new \RuntimeException(message: 'Sorry, something went wrong during the password reset.'.$e->getMessage().' '.$e->getTraceAsString(), code: $e->getCode(), previous: $e);
            }

            return $this->render(
                view: 'user_profile/user_profile_new_password_send.html.twig', parameters: ['email' => $user->getUserIdentifier(), 'profile' => 'password']
            );
        }

        return ['form' => $form->createView(), 'profile' => 'password'];
    }

    /**
     * Benutzer Profil.
     */
    #[Template('user_profile/user_profile_change_mail.html.twig')]
    #[Route(path: '/user-profile/change-mail')]
    /**
     * @return array<string, mixed>|Response
     */
    public function userProfileChangeMail(Request $request, UserPasswordHasherInterface $hasher): array|Response
    {
        $form = $this->createForm(
            type: EmailEditType::class,
            data: []
        );

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            /** @var User $user */
            $user = $this->getUser();
            $data = $form->getData();

            $userRepo = $this->manager->getRepository(User::class);

            $existingUser = $userRepo->findOneBy(criteria: ['email' => $data['new_email']]);

            if (is_null(value: $existingUser)) {
                if ($hasher->isPasswordValid($user, $data['current_password'])) {
                    $user->setEmail(email: $data['new_email']);
                    $this->manager->persist($user);
                    $this->manager->flush();
                }

                return $this->render(
                    view: 'user_profile/user_profile_mail_saved.html.twig', parameters: ['email' => $user->getUserIdentifier(), 'profile' => 'email']
                );
            } else {
                $form->addError(
                    new FormError(message: 'This e-mail address is already in use. You need to enter a different one.')
                );
            }
        }

        return ['form' => $form->createView(), 'profile' => 'email'];
    }

    #[Template('user_profile/user_profile_change_locale.html.twig')]
    #[Route(path: '/user-profile/change-locale')]
    /**
     * @return RedirectResponse|array<string, mixed>
     */
    public function userProfileChangeLocale(Request $request): RedirectResponse|array
    {
        $form = $this->createForm(type: LocaleEditType::class, data: []);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $data = $form->getData();
            $this->setUserLocale(locale: $data['locale']);

            return $this->redirect(url: '/user-profile/change-locale');
        }

        return ['form' => $form->createView(), 'profile' => 'locale'];
    }

    private function setUserLocale(string $locale): void
    {
        /** @var User $user */
        $user = $this->getUser();
        $user->setLocale(locale: $locale);
        $this->manager->persist($user);
        $this->manager->flush();
    }
}
